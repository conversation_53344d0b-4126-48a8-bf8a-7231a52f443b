<template>
  <el-dialog
    :title="$T('选择管网设备')"
    :visible.sync="dialogVisible"
    width="640px"
    :append-to-body="true"
    :close-on-click-modal="false"
  >
    <div class="dialog-content">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          :placeholder="$T('请输入设备名称')"
          class="search-input"
          clearable
          @input="handleSearch"
        >
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>
      </div>

      <!-- 管网设备列表 -->
      <el-table
        :data="networkDevices"
        :loading="loading"
        style="width: 100%; margin-top: 16px"
        height="400"
        ref="networkDevicesTable"
        row-key="treeId"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" :label="$T('设备名称')" min-width="150" />
        <el-table-column :label="$T('设备类型')" width="120">
          <template slot-scope="scope">
            {{ getNetworkDeviceTypeName(scope.row.modellabel) }}
          </template>
        </el-table-column>
        <el-table-column prop="deviceId" :label="$T('设备ID')" width="120" />
      </el-table>
    </div>

    <div slot="footer">
      <el-button @click="handleCancel">
        {{ $T("取消") }}
      </el-button>
      <el-button
        type="primary"
        @click="handleConfirm"
        :disabled="selectedDevices.length === 0"
      >
        {{ $T("确定") }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "NetworkDeviceSelectDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    networkDevices: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    preSelectedDevices: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      searchKeyword: "",
      selectedDevices: [],
      searchTimer: null
    };
  },
  watch: {
    visible: {
      handler(newVal) {
        this.dialogVisible = newVal;
        if (newVal) {
          this.selectedDevices = [...this.preSelectedDevices];
          this.$nextTick(() => {
            this.setDeviceSelection();
          });
        }
      },
      immediate: true
    },
    dialogVisible(newVal) {
      this.$emit("update:visible", newVal);
    },
    networkDevices: {
      handler() {
        this.$nextTick(() => {
          this.setDeviceSelection();
        });
      }
    }
  },
  methods: {
    // 搜索处理
    handleSearch() {
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.$emit("search", this.searchKeyword);
      }, 500);
    },

    // 设备选择改变
    handleSelectionChange(selection) {
      this.selectedDevices = selection;
    },

    // 设置设备选中状态
    setDeviceSelection() {
      if (!this.$refs.networkDevicesTable || !this.selectedDevices.length) {
        return;
      }

      // 清空当前选中状态
      this.$refs.networkDevicesTable.clearSelection();

      // 根据treeId匹配并设置选中状态
      this.selectedDevices.forEach(selectedDevice => {
        const matchedDevice = this.networkDevices.find(device => {
          return device.treeId === selectedDevice.treeId;
        });

        if (matchedDevice) {
          this.$refs.networkDevicesTable.toggleRowSelection(
            matchedDevice,
            true
          );
        }
      });
    },

    // 取消处理
    handleCancel() {
      this.dialogVisible = false;
    },

    // 确认处理
    handleConfirm() {
      if (this.selectedDevices.length === 0) {
        this.$message.warning(this.$T("请选择管网设备"));
        return;
      }

      this.$emit("confirm", this.selectedDevices);
      this.dialogVisible = false;
    },

    // 根据管网设备类型标识获取中文名称
    getNetworkDeviceTypeName(modelLabel) {
      if (!modelLabel) return "未知类型";

      // 从deviceTypeUtils.js中获取管网设备类型映射
      try {
        const { MODEL_LABEL_MAP } = require("@/utils/deviceTypeUtils");
        return MODEL_LABEL_MAP[modelLabel] || modelLabel || "未知类型";
      } catch (error) {
        return modelLabel || "未知类型";
      }
    }
  },
  beforeDestroy() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }
};
</script>

<style scoped>
.dialog-content {
  width: 100%;
}

.search-bar {
  margin-bottom: var(--J2);
}

.search-input {
  width: 300px;
}

/* 弹窗样式 */
::v-deep .el-dialog__wrapper {
  z-index: 3000 !important;
}

::v-deep .el-dialog {
  background: var(--BG1);
  border-radius: var(--Ra1);
}

::v-deep .el-dialog__header {
  background: var(--BG1);
  padding: var(--J3) var(--J4) var(--J2) var(--J4);
}

::v-deep .el-dialog__title {
  color: var(--T1);
  font-size: var(--Ac);
  font-weight: 600;
}

::v-deep .el-dialog__body {
  padding: var(--J3);
  background: var(--BG1);
}

::v-deep .el-dialog__footer {
  background: var(--BG1);
  padding: var(--J2) var(--J4) var(--J3) var(--J4);
  text-align: right;
}

/* 表格样式 */
::v-deep .el-table__body tr.disabled-row {
  background-color: var(--BG3);
  color: var(--T4);
}

::v-deep .el-table__body tr.selected-row {
  background-color: var(--BG2);
}
</style>

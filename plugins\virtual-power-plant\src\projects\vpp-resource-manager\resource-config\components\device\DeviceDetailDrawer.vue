<template>
  <el-drawer
    :title="$T('详情')"
    :visible.sync="drawerVisible"
    direction="rtl"
    size="960px"
    @close="handleClose"
  >
    <div class="detail-content" v-if="deviceDetail">
      <!-- 设备基本信息 -->
      <div class="basic-info-section">
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">{{ $T("设备名称") }}</div>
            <div class="info-value">
              {{ deviceDetail.deviceName || "--" }}
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">{{ $T("设备类型") }}</div>
            <div class="info-value">
              {{ getDeviceTypeNameById(deviceDetail.deviceType) || "--" }}
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">{{ $T("额定功率") }} (kW)</div>
            <div class="info-value">
              {{ deviceDetail.ratedPower || "--" }}
            </div>
          </div>
        </div>
      </div>

      <!-- 管网设备列表 -->
      <div class="device-list-section">
        <div class="list-title">{{ $T("管网设备列表") }}</div>
        <el-table
          :data="networkDeviceList"
          style="width: 100%"
          empty-text="暂无关联的管网设备"
          stripe
          border
        >
          <el-table-column
            type="index"
            label="序号"
            width="80"
            align="center"
          />
          <el-table-column
            prop="name"
            :label="$T('管网设备名称')"
            min-width="200"
          />
          <el-table-column
            prop="modelLabelChinese"
            :label="$T('设备类型')"
            width="150"
          />
        </el-table>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { getEnumLabel } from "@/utils/enumManager";

export default {
  name: "DeviceDetailDrawer",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    deviceDetail: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      drawerVisible: false,
      networkDeviceList: []
    };
  },
  watch: {
    visible: {
      handler(newVal) {
        this.drawerVisible = newVal;
        if (newVal && this.deviceDetail) {
          this.processNetworkDeviceList();
        }
      },
      immediate: true
    },
    drawerVisible(newVal) {
      this.$emit("update:visible", newVal);
    },
    deviceDetail: {
      handler(newVal) {
        if (newVal && this.drawerVisible) {
          this.processNetworkDeviceList();
        }
      },
      deep: true
    }
  },
  methods: {
    // 处理关联的管网设备列表
    processNetworkDeviceList() {
      if (
        this.deviceDetail &&
        this.deviceDetail.monitor_device_relations &&
        this.deviceDetail.monitor_device_relations.length > 0
      ) {
        this.networkDeviceList = this.deviceDetail.monitor_device_relations.map(
          device => ({
            name: device.name || "--",
            deviceId: device.deviceId,
            modelLabel: device.modellabel,
            modelLabelChinese: this.getNetworkDeviceTypeName(device.modellabel), // 转换为中文
            rate: device.rate
          })
        );
      } else {
        // 如果没有关联的管网设备，显示空状态
        this.networkDeviceList = [];
      }
    },

    // 根据设备类型ID获取名称
    getDeviceTypeNameById(deviceTypeId) {
      return getEnumLabel("VPP_DEVICE_TYPE", deviceTypeId) || "未知类型";
    },

    // 根据管网设备类型标识获取中文名称
    getNetworkDeviceTypeName(modelLabel) {
      if (!modelLabel) return "未知类型";

      // 从deviceTypeUtils.js中获取管网设备类型映射
      try {
        const { MODEL_LABEL_MAP } = require("@/utils/deviceTypeUtils");
        return MODEL_LABEL_MAP[modelLabel] || modelLabel || "未知类型";
      } catch (error) {
        return modelLabel || "未知类型";
      }
    },

    // 抽屉关闭处理
    handleClose() {
      this.$emit("close");
    }
  }
};
</script>

<style scoped>
.detail-content {
  padding: var(--J3);
  background: var(--BG1);
  height: 100%;
}

/* 设备基本信息样式 */
.basic-info-section {
  margin-bottom: var(--J3);
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--J4);
  margin-bottom: var(--J2);
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  color: var(--T2);
  font-size: var(--Aa);
  font-weight: 400;
  margin-bottom: var(--J1);
  line-height: var(--J3);
}

.info-value {
  color: var(--T1);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
  min-height: var(--J3);
}

/* 管网设备列表样式 */
.device-list-section {
  margin-bottom: var(--J4);
}

.list-title {
  color: var(--T1);
  font-size: var(--J2);
  font-weight: 500;
  margin-bottom: var(--J2);
  line-height: var(--J4);
}
</style>
